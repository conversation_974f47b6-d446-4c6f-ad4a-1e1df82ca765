import { memo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { IRequest } from '@c-visitor/types';

interface RequestHeaderProps {
  selectedRequest: IRequest | null;
  onReject: () => void;
  onApprove: () => void;
}

/**
 * Header component for the request details page
 * Shows the title and action buttons based on request status
 */
const RequestHeaderComponent = ({ selectedRequest, onReject, onApprove }: RequestHeaderProps) => {
  return (
    <div className='flex justify-start items-center flex-grow-0 flex-shrink-0 w-full gap-3'>
      <div className='flex justify-start items-start flex-grow relative gap-2'>
        <p className='flex-grow-0 flex-shrink-0 text-lg font-semibold text-left text-[#1f2329]'>
          Access Session Details
        </p>
      </div>
      <div className='flex justify-start items-center flex-grow-0 flex-shrink-0 gap-3'>
        {selectedRequest?.status === 'PENDING' && (
          <>
            <div className='flex justify-start items-start flex-grow-0 flex-shrink-0'>
              <Button
                variant='outline'
                className='flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 px-3 py-1.5 rounded-md border border-[#e03e59] cursor-pointer'
                onClick={onReject}
              >
                <p className='flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#e03e59] '>Reject</p>
              </Button>
            </div>
            <div className='flex justify-start items-start flex-grow-0 flex-shrink-0'>
              <Button
                variant='default'
                className='flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 px-3 py-1.5 rounded-md bg-[#02b875] cursor-pointer'
                onClick={onApprove}
              >
                <p className='flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-white '>Approve</p>
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

/**
 * Memoized version of RequestHeader to prevent unnecessary re-renders
 * Only re-renders when the request status changes
 */
export const RequestHeader = memo(RequestHeaderComponent, (prevProps, nextProps) => {
  // Only re-render if the request status changes
  return prevProps.selectedRequest?.status === nextProps.selectedRequest?.status
  // We don't compare function props like onReject and onApprove
  // because they should be wrapped in useCallback by the parent component
});
