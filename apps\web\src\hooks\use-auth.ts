import { useState, useEffect } from 'react';
import { useApiMutation, useApiQuery } from './use-api-query';
import { useQueryClient } from '@tanstack/react-query';

// Types based on actual API responses
export interface UserInfo {
  id: string;
  email: string;
  fullName: string;
  roles: string[];
}

export interface SignUpInput {
  email: string;
  fullName: string;
  password: string;
}

export interface SignInInput {
  email: string;
  password: string;
}

export interface SignInApiResponse {
  code: string;
  success: boolean;
  statusCode: number;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    user: UserInfo;
  };
}

export interface SignInResponse {
  user: UserInfo;
  token: string;
  refreshToken: string;
}

export interface RefreshTokenInput {
  refreshToken: string;
}

export interface ForgotPasswordInput {
  email: string;
}

export interface ResetPasswordInput {
  token: string;
  password: string;
}

export interface VerifyEmailInput {
  token: string;
}

export function useAuth() {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const queryClient = useQueryClient();

  // Sign up mutation
  const signUpMutation = useApiMutation<UserInfo, SignUpInput>('/api/identity/signup', 'POST');

  // Sign in mutation
  const signInMutation = useApiMutation<{
    accessToken: string;
    refreshToken: string;
    user: UserInfo;
  }, SignInInput>('/api/identity/signin', 'POST', {
    onSuccess: (data) => {
      localStorage.setItem('auth_token', data.accessToken);
      localStorage.setItem('refresh_token', data.refreshToken);
      setUser(data.user);
      queryClient.invalidateQueries();
    },
  });

  // Refresh token mutation
  const refreshTokenMutation = useApiMutation<SignInResponse, RefreshTokenInput>('/api/identity/refresh-token', 'POST', {
    onSuccess: (data) => {
      localStorage.setItem('auth_token', data.token);
      localStorage.setItem('refresh_token', data.refreshToken);
      setUser(data.user);
    },
  });

  // Sign out mutation
  const signOutMutation = useApiMutation<void, {}>('/api/identity/signout', 'POST', {
    onSuccess: () => {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      setUser(null);
      queryClient.clear();
    },
  });

  // Verify email mutation
  const verifyEmailMutation = useApiMutation<void, VerifyEmailInput>('/api/identity/verify-email', 'POST');

  // Forgot password mutation
  const forgotPasswordMutation = useApiMutation<void, ForgotPasswordInput>('/api/identity/forgot-password', 'POST');

  // Reset password mutation
  const resetPasswordMutation = useApiMutation<void, ResetPasswordInput>('/api/identity/reset-password', 'POST');

  // Get profile query
  const useGetProfile = () => {
    return useApiQuery<UserInfo>(['profile'], '/api/identity/profile', {}, {
      enabled: !!localStorage.getItem('auth_token'),
    });
  };

  // Function to check if user is authenticated
  const isAuthenticated = (): boolean => {
    return !!localStorage.getItem('auth_token');
  };

  // Function to get token
  const getToken = (): string | null => {
    return localStorage.getItem('auth_token');
  };

  // Function to get refresh token
  const getRefreshToken = (): string | null => {
    return localStorage.getItem('refresh_token');
  };

  // Function to decode JWT token and extract user info
  const decodeToken = (token: string): UserInfo | null => {
    try {
      // JWT tokens have 3 parts separated by dots
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      // Decode the payload (second part)
      const payload = parts[1];
      // Add padding if needed for base64 decoding
      const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
      const decodedPayload = atob(paddedPayload);
      const parsedPayload = JSON.parse(decodedPayload);

      // Extract user info from JWT payload
      return {
        id: parsedPayload.sub || '',
        email: parsedPayload.email || '',
        fullName: parsedPayload.fullName || parsedPayload.name || '',
        roles: parsedPayload.roles || []
      };
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  };

  // Function to load user from token
  const loadUserFromToken = () => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      const userInfo = decodeToken(token);
      if (userInfo) {
        setUser(userInfo);
      } else {
        // Invalid token, clear it
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        setUser(null);
      }
    } else {
      setUser(null);
    }
  };

  // Initialize user on mount
  useEffect(() => {
    loadUserFromToken();
    setIsLoading(false);
  }, []);

  return {
    // State
    user,
    isLoading,

    // Auth methods
    signUp: signUpMutation.mutate,
    signUpAsync: signUpMutation.mutateAsync,
    signUpResult: signUpMutation,

    signIn: signInMutation.mutate,
    signInAsync: signInMutation.mutateAsync,
    signInResult: signInMutation,

    signOut: signOutMutation.mutate,
    signOutAsync: signOutMutation.mutateAsync,
    signOutResult: signOutMutation,

    refreshToken: refreshTokenMutation.mutate,
    refreshTokenAsync: refreshTokenMutation.mutateAsync,
    refreshTokenResult: refreshTokenMutation,

    verifyEmail: verifyEmailMutation.mutate,
    verifyEmailAsync: verifyEmailMutation.mutateAsync,
    verifyEmailResult: verifyEmailMutation,

    forgotPassword: forgotPasswordMutation.mutate,
    forgotPasswordAsync: forgotPasswordMutation.mutateAsync,
    forgotPasswordResult: forgotPasswordMutation,

    resetPassword: resetPasswordMutation.mutate,
    resetPasswordAsync: resetPasswordMutation.mutateAsync,
    resetPasswordResult: resetPasswordMutation,

    // Queries
    useGetProfile,

    // Utility methods
    isAuthenticated,
    getToken,
    getRefreshToken,
    loadUserFromToken,
    decodeToken,
  };
}
