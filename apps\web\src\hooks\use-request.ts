import { useApiQuery, useApiMutation } from './use-api-query';
import { IRequest, IReference, RequestStatus } from '@c-visitor/types';

// Types based on actual entities and API
export interface ReferenceWithRecognitionStats {
  reference: IReference;
  recognitionCount: number;
  firstCheckIn: Date | null;
  lastCheckOut: Date | null;
}

export interface RequestWithReferences {
  request: IRequest;
  references: IReference[];
  referencesWithStats?: ReferenceWithRecognitionStats[];
}

export interface UpsertRequestInput {
  id?: string;
  code?: number;
  companyName: string;
  purpose: string;
  contactName: string;
  contactDepartment: string;
  supervisorName: string;
  supervisorDepartment: string;
  supervisorEmail: string;
  supervisorPhone: string;
  timeIn: string;
  timeOut?: string;
  deviceInformationBroughtIn?: string;
  note?: string;
  otherRequest?: string;
  companyId?: string;
  references?: Partial<IReference>[];
}

export interface ChangeStatusInput {
  status: RequestStatus;
  changeStatusBy?: string;
  note?: string;
  reason?: string; // Rejection reason (required when status is REJECTED)
}

export interface GetRequestsParams {
  pageIndex?: number;
  pageSize?: number;
  keyword?: string;
  status?: RequestStatus | string;
  timeIn?: Date | string;
  timeOut?: Date | string;
  companyName?: string;
  supervisorEmail?: string;
  // New search and filter fields
  search?: string;
  // Clear date range filters
  startDate?: Date | string;
  endDate?: Date | string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    total: number;
    pageIndex: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface GetRequestsResponse {
  data: PaginatedResponse<IRequest>;
}

export function useRequest() {
  // Direct API call for imperative usage
  const getRequests = async (params?: GetRequestsParams): Promise<GetRequestsResponse> => {
    const queryParams: Record<string, any> = {};

    if (params?.pageIndex) queryParams.pageIndex = params.pageIndex;
    if (params?.pageSize) queryParams.pageSize = params.pageSize;
    if (params?.keyword) queryParams.keyword = params.keyword;
    if (params?.status) queryParams.status = params.status;
    if (params?.timeIn) queryParams.timeIn = params.timeIn;
    if (params?.timeOut) queryParams.timeOut = params.timeOut;
    if (params?.companyName) queryParams.companyName = params.companyName;
    if (params?.supervisorEmail) queryParams.supervisorEmail = params.supervisorEmail;

    // New search and filter fields
    if (params?.search) queryParams.search = params.search;

    // Clear date range filters
    if (params?.startDate) {
      queryParams.startDate = params.startDate instanceof Date
        ? params.startDate.toISOString()
        : params.startDate;
    }
    if (params?.endDate) {
      queryParams.endDate = params.endDate instanceof Date
        ? params.endDate.toISOString()
        : params.endDate;
    }

    // Build query string
    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/request${queryString ? `?${queryString}` : ''}`;

    console.log('Making API request to:', url);
    console.log('With params:', queryParams);

    const token = localStorage.getItem('auth_token');
    console.log('Using token:', token ? 'EXISTS' : 'NOT FOUND');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    console.log('Response status:', response.status);
    console.log('Response content-type:', response.headers.get('content-type'));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const data = await response.json();
    console.log('API Response data:', data);
    return data;
  };

  // Get requests with filters
  const useGetRequests = (params?: GetRequestsParams) => {
    const queryParams: Record<string, any> = {};

    if (params?.pageIndex) queryParams.pageIndex = params.pageIndex;
    if (params?.pageSize) queryParams.pageSize = params.pageSize;
    if (params?.keyword) queryParams.keyword = params.keyword;
    if (params?.status) queryParams.status = params.status;
    if (params?.timeIn) queryParams.timeIn = params.timeIn;
    if (params?.timeOut) queryParams.timeOut = params.timeOut;
    if (params?.companyName) queryParams.companyName = params.companyName;
    if (params?.supervisorEmail) queryParams.supervisorEmail = params.supervisorEmail;

    // New search and filter fields
    if (params?.search) queryParams.search = params.search;

    // Clear date range filters
    if (params?.startDate) {
      queryParams.startDate = params.startDate instanceof Date
        ? params.startDate.toISOString()
        : params.startDate;
    }
    if (params?.endDate) {
      queryParams.endDate = params.endDate instanceof Date
        ? params.endDate.toISOString()
        : params.endDate;
    }

    // Create a stable query key
    const queryKey = ['requests'];
    if (params?.pageIndex) queryKey.push(`pageIndex:${params.pageIndex}`);
    if (params?.pageSize) queryKey.push(`pageSize:${params.pageSize}`);
    if (params?.keyword) queryKey.push(`keyword:${params.keyword}`);
    if (params?.status) queryKey.push(`status:${params.status}`);
    if (params?.timeIn) queryKey.push(`timeIn:${params.timeIn}`);
    if (params?.timeOut) queryKey.push(`timeOut:${params.timeOut}`);
    if (params?.companyName) queryKey.push(`companyName:${params.companyName}`);
    if (params?.supervisorEmail) queryKey.push(`supervisorEmail:${params.supervisorEmail}`);
    if (params?.search) queryKey.push(`search:${params.search}`);
    if (params?.startDate) queryKey.push(`startDate:${params.startDate}`);
    if (params?.endDate) queryKey.push(`endDate:${params.endDate}`);

    return useApiQuery<GetRequestsResponse>(queryKey, '/api/request', queryParams);
  };

  // Get request by ID with embedded references (simplified API)
  const useGetRequest = (id: string) => {
    return useApiQuery<IRequest>(
      ['request', id],
      `/api/request/${id}`,
      undefined,
      {
        enabled: !!id && id !== '', // Only run query if ID is provided and not empty
        retry: 1, // Retry once on failure
        staleTime: 0, // Always fetch fresh data
      }
    );
  };

  // Upsert request (create or update) - returns simplified structure
  const useUpsertRequest = () => {
    return useApiMutation<IRequest, UpsertRequestInput>('/api/request/upsert', 'POST', {
      meta: {
        successMessage: 'Yêu cầu đã được lưu thành công',
      },
    });
  };

  // Change request status
  const useChangeRequestStatus = (id: string) => {
    return useApiMutation<IRequest, ChangeStatusInput>(`/api/request/changeStatus/${id}`, 'PUT', {
      meta: {
        successMessage: 'Trạng thái yêu cầu đã được cập nhật',
      },
    });
  };

  return {
    // Direct API calls
    getRequests,

    // React Query hooks
    useGetRequests,
    useGetRequest,
    useUpsertRequest,
    useChangeRequestStatus,
  };
}
