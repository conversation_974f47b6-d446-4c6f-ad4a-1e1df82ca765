import 'reflect-metadata';
import { Request, Response, NextFunction } from 'express';
import {
  ClaimsPrincipal,
  createClaimsPrincipalFromJwt,
} from '../claims-principal.js';
import { UnauthorizedError } from '../http-error.js';
import { instances } from './service.decorator.js';
import { Container } from '../container.js';
import { logger } from '../logger.js';

// Metadata key for authorized decorator
export const AUTHORIZED_METADATA = 'authorized';

// Interface for token verification service
export interface ITokenVerifier {
  verifyToken(token: string): any;
}

// Extend Express Request interface to include body, params, query, user and claimsPrincipal
// Using module augmentation instead of global namespace declaration
declare module 'express' {
  interface Request {
    user?: any;
    claimsPrincipal?: ClaimsPrincipal;
  }
}

/**
 * Decorator that requires authorization for controller methods or entire controllers
 * Extracts user information from Bearer token and makes it available in the request
 *
 * @param options Optional configuration for the authorization
 * @returns A decorator function that can be applied to classes or methods
 */
export function Authorized(options: { roles?: string[] } = {}) {
  return function (
    target: any,
    propertyKey?: string | symbol,
    descriptor?: PropertyDescriptor
  ) {
    // Store metadata about authorization requirement
    if (propertyKey && descriptor) {
      // Method decorator
      Reflect.defineMetadata(
        AUTHORIZED_METADATA,
        { ...options, required: true },
        target,
        propertyKey
      );
    } else {
      // Class decorator
      Reflect.defineMetadata(
        AUTHORIZED_METADATA,
        { ...options, required: true },
        target
      );
    }

    return descriptor || target;
  };
}

/**
 * Middleware function that handles authorization
 * This is used internally by the framework to process the @Authorized decorator
 *
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 * @param options Authorization options from decorator metadata
 */
export async function handleAuthorization(
  req: Request,
  _res: Response,
  next: NextFunction,
  options: { roles?: string[]; required: boolean }
): Promise<void> {
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      if (options.required) {
        throw new UnauthorizedError('Missing or invalid authorization header');
      } else {
        return next(); // Skip if authorization is not required
      }
    }

    // Extract token
    const token = authHeader.split(' ')[1];
    if (!token) {
      if (options.required) {
        throw new UnauthorizedError('Missing token');
      } else {
        return next(); // Skip if authorization is not required
      }
    }

    // Find a service that can verify tokens
    // This assumes there's a service registered with a verifyToken method
    // The actual service name may vary based on your application
    const tokenVerifier = findTokenVerifier();

    if (!tokenVerifier) {
      throw new UnauthorizedError('Authentication service unavailable');
    }

    // Verify token
    let payload;
    try {
      payload = tokenVerifier.verifyToken(token);
    } catch (error) {
      // If token verification fails, throw an UnauthorizedError
      throw new UnauthorizedError('Invalid token');
    }

    // Create ClaimsPrincipal from token payload
    const claimsPrincipal = createClaimsPrincipalFromJwt(payload);

    // Attach ClaimsPrincipal to request
    req.claimsPrincipal = claimsPrincipal;

    // For backward compatibility, also attach user info to request
    req.user = {
      id: payload.sub,
      email: payload.email,
      // Add any other properties from the token payload
      ...payload,
    };

    // Check roles if specified
    if (options.roles && options.roles.length > 0) {
      // First check if the roles are in the token claims
      const hasRoleInClaims = options.roles.some((role) =>
        claimsPrincipal.isInRole(role)
      );

      if (hasRoleInClaims) {
        return next(); // User has required role in claims
      }

      // If roles not in claims, check database as fallback
      const hasRequiredRole = await checkUserRoles(payload.sub, options.roles);

      if (!hasRequiredRole) {
        throw new UnauthorizedError('Insufficient permissions');
      }
    }

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * Helper function to find a service that can verify tokens
 * This is a simple implementation that looks for a service with a verifyToken method
 */
function findTokenVerifier(): ITokenVerifier | null {
  // Try to find a service with a verifyToken method
  // This is a simplified approach and may need to be adjusted based on your DI setup
  const services: any[] = [];

  // Collect all instantiated services
  for (const [_, metadata] of instances.entries()) {
    if (metadata.instance) {
      services.push(metadata.instance);
    }
  }

  // Find a service with verifyToken method
  for (const service of services) {
    if (typeof service.verifyToken === 'function') {
      return service as ITokenVerifier;
    }
  }

  // If no service is found, try to find one by a common name
  try {
    return Container.get('IdentityService') as ITokenVerifier;
  } catch (error) {
    // Silently handle the error if the service is not found
    // This is expected in some cases
  }

  return null;
}

/**
 * Helper function to check if a user has any of the required roles
 * This is a placeholder implementation that should be replaced with your actual role checking logic
 */
async function checkUserRoles(
  userId: string,
  requiredRoles: string[]
): Promise<boolean> {
  try {
    // Try to find a service that can check user roles
    // This is a simplified approach and may need to be adjusted based on your application
    const identityService = Container.get('IdentityService') as any;

    if (
      !identityService ||
      typeof identityService.getUserWithRoles !== 'function'
    ) {
      // Log the error but don't expose it to the client
      logger.error('Identity service not found or missing getUserWithRoles method');
      return false;
    }

    const user = await identityService.getUserWithRoles(userId);

    if (!user || !user.roles) {
      return false;
    }

    // Check if user has any of the required roles
    return requiredRoles.some((role) => {
      if (!Array.isArray(user.roles)) {
        return false;
      }
      return user.roles.some((userRole: any) => {
        if (typeof userRole === 'string') {
          return userRole === role;
        } else if (userRole && typeof userRole === 'object') {
          return userRole.name === role;
        }
        return false;
      });
    });
  } catch (error) {
    // Log the error but don't expose it to the client
    logger.error('Error checking user roles:', { error });
    return false;
  }
}
