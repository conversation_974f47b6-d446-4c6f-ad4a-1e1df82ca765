import jwt from 'jsonwebtoken';
import { type IHttpContext } from '@c-visitor/framework';
import { environment } from '@/configs/environment';

/**
 * Get current user ID from context (for database operations)
 * Fast version that extracts ID directly from JWT without database query
 * @param context HTTP context
 * @returns User ID or null if not authenticated
 */
export async function getCurrentUserId(context: IHttpContext): Promise<string | null> {
  try {
    const authHeader = context.request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      return null;
    }

    // Verify JWT token and extract user ID directly
    // Use the same JWT secret from environment configuration
    const jwtSecret = environment.JWT_SECRET;
    const decoded = jwt.verify(token, jwtSecret) as any;

    if (!decoded || !decoded.sub) {
      return null;
    }

    return decoded.sub;
  } catch (error) {
    console.error('Error extracting user ID from token:', error);
    return null;
  }
}
