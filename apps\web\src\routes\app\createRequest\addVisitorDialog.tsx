import { createFileRoute } from '@tanstack/react-router'
import { FormDialog, FormDialogActions, FormDialogButton, FormDialogField } from '@/components/shared/form-dialog'
import { DragDropFile } from '@/components/ui/drag-drop-file'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import React from 'react'
// UUID only used for file names, not for MongoDB objects

export const Route = createFileRoute('/app/createRequest/addVisitorDialog')({
  component: AddVisitorDialogComponent,
})

interface AddVisitorDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAddReference: (referencesData: ReferencesData) => void
  referencesData?: ReferencesData | null
}

// Define the visitor data type
export interface ReferencesData {
  id?: string
  fullName: string
  email: string
  phone: string
  unit: string
  cardIdNumber: string
  avatar: string
  cardIdFront: string
  cardIdBack: string
}

// Zod schema for visitor form validation
const addVisitorFormSchema = z.object({
  fullName: z.string().min(1, { message: 'Please enter the name' }),
  email: z
    .string()
    .email({ message: 'Please enter the email information' })
    .min(1, { message: 'Please enter the email information' }),
  phone: z
    .string()
    .min(1, { message: 'Please enter the phone number' })
    .regex(/^[0-9]{9,11}$/, {
      message: 'Please enter the phone number'
    }),
  unit: z.string().min(1, { message: 'Please enter the unit' }),
  cardIdNumber: z.string().min(1, { message: 'Please enter the card ID number' }),
  avatar: z.string().optional(),
  cardIdFront: z.string().min(1, { message: 'Please upload the front side of the card' }),
  cardIdBack: z.string().min(1, { message: 'Please upload the back side of the card' })
})

type AddVisitorFormValues = z.infer<typeof addVisitorFormSchema>

function AddVisitorDialogComponent() {
  // This component will be used as a dialog, so we'll export the main component
  return null
}

export function AddReferenceDialog({
  open,
  onOpenChange,
  onAddReference: onAddVisitor,
  referencesData: referencesData
}: AddVisitorDialogProps) {
  const form = useForm<AddVisitorFormValues>({
    resolver: zodResolver(addVisitorFormSchema),
    defaultValues: {
      fullName: referencesData?.fullName || '',
      email: referencesData?.email || '',
      phone: referencesData?.phone || '',
      unit: referencesData?.unit || '',
      cardIdNumber: referencesData?.cardIdNumber || '',
      avatar: referencesData?.avatar || '',
      cardIdFront: referencesData?.cardIdFront || '',
      cardIdBack: referencesData?.cardIdBack || ''
    }
  })

  // Reset form when dialog opens/closes or data changes
  React.useEffect(() => {
    if (open) {
      form.reset({
        fullName: referencesData?.fullName || '',
        email: referencesData?.email || '',
        phone: referencesData?.phone || '',
        unit: referencesData?.unit || '',
        cardIdNumber: referencesData?.cardIdNumber || '',
        avatar: referencesData?.avatar || '',
        cardIdFront: referencesData?.cardIdFront || '',
        cardIdBack: referencesData?.cardIdBack || ''
      })
    }
  }, [open, referencesData, form])

  const onSubmit = (data: AddVisitorFormValues) => {
    // Create visitor data object
    const submittedVisitor: ReferencesData = {
      ...data,
      avatar: data.avatar || '', // Ensure avatar is always a string
      id: referencesData?.id || Date.now().toString() // Use existing ID or generate temporary ID
    }

    // Pass the visitor data to the parent component
    onAddVisitor(submittedVisitor)

    // Reset form
    form.reset()

    // Close dialog
    onOpenChange(false)
  }

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={referencesData ? 'Update Person (Entering/Exiting)' : 'Add Person (Entering/Exiting)'}
      className="!max-w-[1000px] !w-[800px]"
      footer={
        <FormDialogActions>
          <FormDialogButton
            variant="secondary"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </FormDialogButton>
          <FormDialogButton
            variant="primary"
            onClick={form.handleSubmit(onSubmit)}
          >
            {referencesData ? 'Update' : 'Add'}
          </FormDialogButton>
        </FormDialogActions>
      }
    >
      <Form {...form}>
        <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-6 py-4'>
          <div className='flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-6'>
            {/* Left Column - Personal Information */}
            <div className='flex flex-col justify-start items-start flex-grow gap-4'>
              <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative'>
                <div className='flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]' />
                <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2'>
                  <p className='flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]'>
                    Personal Information
                  </p>
                </div>
              </div>

              <div className='flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4'>
                <div className='flex justify-start items-center flex-grow gap-4'>
                  <div className='flex flex-col justify-start items-start flex-grow gap-1'>
                    <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                      <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                        <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                          Full name
                        </p>
                        <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                      </div>
                    </div>
                    <FormField
                      control={form.control}
                      name='fullName'
                      render={({ field }) => (
                        <FormItem className='w-full'>
                          <FormControl>
                            <Input placeholder='Enter full name' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                <div className='flex justify-start items-center flex-grow gap-4'>
                  <div className='flex flex-col justify-start items-start flex-grow gap-1'>
                    <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                      <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                        <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                          Email
                        </p>
                        <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                      </div>
                    </div>
                    <FormField
                      control={form.control}
                      name='email'
                      render={({ field }) => (
                        <FormItem className='w-full'>
                          <FormControl>
                            <Input placeholder='Enter email address' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-4'>
                <div className='flex flex-col justify-start items-start flex-grow gap-1'>
                  <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                    <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                      <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                        Phone number
                      </p>
                      <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name='phone'
                    render={({ field }) => (
                      <FormItem className='w-full'>
                        <FormControl>
                          <Input placeholder='Enter phone number' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-4'>
                <div className='flex flex-col justify-start items-start flex-grow gap-1'>
                  <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                    <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                      <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                        Organization
                      </p>
                      <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name='unit'
                    render={({ field }) => (
                      <FormItem className='w-full'>
                        <FormControl>
                          <Input placeholder='Enter organization' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-4'>
                <div className='flex flex-col justify-start items-start flex-grow gap-1'>
                  <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                    <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                      <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                        ID Number
                      </p>
                      <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name='cardIdNumber'
                    render={({ field }) => (
                      <FormItem className='w-full'>
                        <FormControl>
                          <Input placeholder='Enter ID number' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-col self-stretch flex-grow-0 flex-shrink-0 gap-1'>
                <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                  <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                    <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                      ID Card images:
                    </p>
                    <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                  </div>
                </div>
                <div className='flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4'>
                  <FormField
                    control={form.control}
                    name='cardIdFront'
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <DragDropFile
                            text='Upload image front side'
                            className='h-[102px] w-[182px] cursor-pointer'
                            initialImage={field.value}
                            onImageUrl={(base64) => {
                              if (base64) {
                                form.setValue('cardIdFront', base64)
                              } else {
                                form.setValue('cardIdFront', '')
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name='cardIdBack'
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <DragDropFile
                            text='Upload image back side'
                            className='h-[102px] w-[182px] cursor-pointer'
                            initialImage={field.value}
                            onImageUrl={(base64) => {
                              if (base64) {
                                form.setValue('cardIdBack', base64)
                              } else {
                                form.setValue('cardIdBack', '')
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Right Column - Portrait Photo */}
            <div className='flex justify-start items-center flex-grow gap-4'>
              <div className='flex flex-col justify-start items-start flex-grow gap-1'>
                <div className='flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden'>
                  <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
                    <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>
                      Portrait photo
                    </p>
                    <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
                  </div>
                </div>
                <FormField
                  control={form.control}
                  name='avatar'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <DragDropFile
                          text='Upload photo'
                          className='h-[200px] w-[200px] cursor-pointer'
                          initialImage={field.value}
                          onImageUrl={(base64) => {
                            if (base64) {
                              form.setValue('avatar', base64)
                            } else {
                              form.setValue('avatar', '')
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      </Form>
    </FormDialog>
  )
}
