import { ProtectedLayout } from '@/components/layout';
import {
  createFileRoute,
  Link,
  Outlet,
  useMatches,
} from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/shared/data-table';
import { ColumnDef } from '@tanstack/react-table';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Check, Phone, Search, Smartphone, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import React from 'react';
import { DateRange } from 'react-day-picker';
import { useRequest, GetRequestsParams } from '@/hooks/use-request';
import { IRequest } from '@c-visitor/types';
import { toast } from 'sonner';
import { Calendar } from '@/components/ui/calendar';

export const Route = createFileRoute('/app/requests')({
  component: RequestsComponent,
});

// Utility function to format dates
const formatDateString = (
  dateValue: string | Date,
  includeTime = true,
): string => {
  if (!dateValue) return '';

  const date = new Date(dateValue);

  // Format day, month, year
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  // Format date part
  const datePart = `${day}/${month}/${year}`;

  if (!includeTime) {
    return datePart;
  }

  // Format hours, minutes, seconds
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${datePart} ${hours}:${minutes}:${seconds}`;
};

function RequestsComponent() {
  // Always declare all hooks first, before any conditional logic
  const [openStatusFilterBox, setOpenStatusFilterBox] = React.useState(false);
  const [selectedStatusFilter, setSelectedStatusFilter] = React.useState('');
  const [selectedDateFilter, setSelectedDateFilter] = React.useState<
    DateRange | undefined
  >(undefined);

  const [draftStatus, setDraftStatus] = React.useState(selectedStatusFilter);
  const [draftDate, setDraftDate] = React.useState(selectedDateFilter);

  const [searchKeyword, setSearchKeyword] = React.useState('');

  const [open, setOpen] = React.useState(false);

  // Pagination state
  const [pageIndex, setPageIndex] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  const [totalItems, setTotalItems] = React.useState(0);
  const [totalPages, setTotalPages] = React.useState(0);

  // Request data state
  const [requestData, setRequestData] = React.useState<IRequest[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);

  // Get the request service functions directly
  const { getRequests } = useRequest();

  // Build query params function
  const buildQueryParams = React.useCallback(
    (customPageIndex?: number, customPageSize?: number): GetRequestsParams => {
      const params: GetRequestsParams = {
        pageIndex: customPageIndex ?? pageIndex,
        pageSize: customPageSize ?? pageSize,
      };

      // Add status filter if selected
      if (selectedStatusFilter && selectedStatusFilter !== 'all') {
        params.status = selectedStatusFilter.toUpperCase();
      }

      // Add date filter if selected (use new clear startDate/endDate parameters)
      if (selectedDateFilter?.from) {
        params.startDate = selectedDateFilter.from;

        // If no 'to' date is selected, use the same date as 'endDate' for single day filter
        if (selectedDateFilter?.to) {
          params.endDate = selectedDateFilter.to;
        } else {
          // Single day selection - use the same date for both start and end
          params.endDate = selectedDateFilter.from;
        }

        // Debug: Log date filter
        console.log('Date Filter Applied:', {
          from: selectedDateFilter.from,
          to: selectedDateFilter.to,
          startDate: params.startDate,
          endDate: params.endDate,
          startDateISO: params.startDate.toISOString(),
          endDateISO: params.endDate.toISOString(),
        });
      }

      // Add search keyword if provided (use new search field)
      if (searchKeyword && searchKeyword.trim() !== '') {
        params.search = searchKeyword.trim();
      }

      return params;
    },
    [
      pageIndex,
      pageSize,
      selectedStatusFilter,
      selectedDateFilter,
      searchKeyword,
    ],
  );

  // Handle API response
  const handleRequestResponse = React.useCallback((response: any) => {
    console.log('API Response:', response);

    const nestedData = response?.data?.data;
    if (response.success && nestedData) {
      const { items, pagination } = nestedData;
      const { total, pageIndex, pageSize, totalPages } = pagination || {};

      // Ensure items is an array
      const safeItems = Array.isArray(items) ? items : [];

      // Update state
      setRequestData(safeItems);
      setTotalItems(total || 0);
      setTotalPages(totalPages || 0);
      return true;
    } else {
      // Show error message
      toast.error('Failed to fetch requests');
      console.error('Error fetching requests:', response.message);
      return false;
    }
  }, []);

  // Handle API error
  const handleRequestError = React.useCallback((error: any) => {
    toast.error('An error occurred while fetching requests');
    console.error('Error fetching requests:', error);
  }, []);

  // Main function to fetch data
  const fetchRequests = React.useCallback(
    async (customPageIndex?: number, customPageSize?: number) => {
      try {
        setIsLoading(true);

        // Build query params
        const params = buildQueryParams(customPageIndex, customPageSize);
        console.log('Fetching requests with params:', params);

        // Call API
        const response = await getRequests(params);

        // Handle response
        return handleRequestResponse(response);
      } catch (error) {
        handleRequestError(error);
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [buildQueryParams, getRequests, handleRequestResponse, handleRequestError],
  );

  // Handle pagination change
  const handlePaginationChange = React.useCallback(
    (newPageIndex: number, newPageSize: number) => {
      // Only call API if page or page size changed
      if (newPageIndex + 1 !== pageIndex || newPageSize !== pageSize) {
        // If page size changed, reset to page 1
        if (newPageSize !== pageSize) {
          // Update state
          setPageSize(newPageSize);
          setPageIndex(1);

          // Call API with page 1 and new page size
          fetchRequests(1, newPageSize);
        } else {
          // Only page changed
          const updatedPageIndex = newPageIndex + 1; // Convert to 1-based indexing

          // Update state
          setPageIndex(updatedPageIndex);

          // Call API with new page
          fetchRequests(updatedPageIndex, pageSize);
        }
      }
    },
    [pageIndex, pageSize, fetchRequests],
  );

  React.useEffect(() => {
    if (searchKeyword === '') {
      setPageIndex(1);
      fetchRequests(1, pageSize);
    }
  }, [searchKeyword]);

  // Fetch requests on initial load
  // React.useEffect(() => {
  //   fetchRequests();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  // Khi bất kỳ filter nào đổi, sẽ tự động gọi API
  React.useEffect(() => {
    fetchRequests(pageIndex, pageSize);
  }, [selectedStatusFilter, selectedDateFilter, pageIndex, pageSize]);

  const handleApply = () => {
    setSelectedStatusFilter(draftStatus);
    setSelectedDateFilter(draftDate);
    setPageIndex(1);
  };

  const clearAllFilters = React.useCallback(() => {
    setDraftStatus('');
    setDraftDate(undefined);
    setSelectedStatusFilter('');
    setSelectedDateFilter(undefined);
    setSearchKeyword('');
    setPageIndex(1);
  }, []);

  // Clear all filters
  // const clearAllFilters = React.useCallback(() => {
  //   // Reset filter values
  //   setSelectedStatusFilter('');
  //   setSelectedDateFilter(undefined);
  //   setSearchKeyword('');

  //   // Reset to page 1 but keep page size
  //   setPageIndex(1);

  //   // Call API with cleared filters
  //   fetchRequests(1, pageSize);
  // }, [fetchRequests, pageSize]);

  // Define table columns
  const columns: ColumnDef<IRequest>[] = [
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="font-medium text-[#26282c]">{row.original.code}</div>
      ),
    },
    {
      accessorKey: 'supervisorName',
      header: 'Assignee',
      cell: ({ row }) => (
        <div className="space-y-1">
          <div className="truncate font-medium text-[#26282c]">
            {row.original.supervisorName}
          </div>
          <div className="flex items-center gap-1.5 text-xs text-[#73787e]">
            <Smartphone className="w-3 h-3" />
            <span>{row.original.supervisorPhone}</span>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'supervisorDepartment',
      header: 'Department',
      cell: ({ row }) => (
        <div className="truncate text-sm text-[#1f2329]">
          {row.original.supervisorDepartment}
        </div>
      ),
    },
    {
      accessorKey: 'timeIn',
      header: 'Entry time',
      cell: ({ row }) => (
        <div className="truncate text-sm text-[#1f2329]">
          {formatDateString(row.original.timeIn)}
        </div>
      ),
    },
    {
      accessorKey: 'timeOut',
      header: 'Exit time',
      cell: ({ row }) => (
        <div className="truncate text-sm text-[#1f2329]">
          {formatDateString(row.original.timeOut)}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        const statusColors = {
          PENDING: '#008FD3',
          APPROVED: '#35C724',
          REJECTED: '#E53C3F',
        };
        const statusLabels = {
          PENDING: 'Pending',
          APPROVED: 'Approved',
          REJECTED: 'Rejected',
        };

        return (
          <div className="flex items-center space-x-2">
            <span
              className="w-2.5 h-2.5 rounded-full inline-block"
              style={{
                backgroundColor:
                  status && status in statusColors
                    ? statusColors[status as keyof typeof statusColors]
                    : '#000',
              }}
            />
            <span className="text-sm text-[#1f2329]">
              {status && status in statusLabels
                ? statusLabels[status as keyof typeof statusLabels]
                : 'Unknown'}
            </span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Action',
      cell: ({ row }) => {
        // MongoDB uses _id, but our interface expects id
        const requestId = row.original.id || (row.original as any)._id || '';

        return (
          <Link
            to="/app/requests/$id"
            params={{ id: requestId }}
            className="text-sm font-medium text-[#008fd3] hover:underline"
          >
            Detail
          </Link>
        );
      },
    },
  ];

  const requestStatusOptions = [
    {
      value: 'all',
      label: 'All',
    },
    {
      value: 'pending',
      label: 'Pending',
    },
    {
      value: 'approved',
      label: 'Approved',
    },
    {
      value: 'rejected',
      label: 'Rejected',
    },
  ];

  // Check if we're on a child route (like /app/requests/$id)
  const matches = useMatches();
  const isChildRoute = matches.some(
    (match) => match.routeId === '/app/requests/$id',
  );

  // If we're on a child route, render the Outlet instead of the requests list
  if (isChildRoute) {
    return <Outlet />;
  }

  return (
    <ProtectedLayout className="bg-white">
      <div className="flex justify-start items-start h-full bg-white">
        <div className="flex flex-col justify-start items-start self-stretch flex-grow gap-4 px-6 py-5 bg-white">
          <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 relative">
            <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#1f2329]">
              List of Access Registrations
            </p>
            <Link to="/app/create-request">
              <Button className="cursor-pointer hover:bg-[#008FD3] bg-[#008FD3]">
                Create
              </Button>
            </Link>
          </div>

          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-2">
            <div className="relative flex-grow-0 flex-shrink-0 w-[250px]">
              <Input
                placeholder="Search by Code, Supervisor Name..."
                className="pl-8"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    // Reset to first page when searching
                    setPageIndex(1);
                    // Fetch requests with the new search keyword
                    fetchRequests(1, pageSize);
                  }
                }}
              />
              <div className="absolute left-2.5 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <Search className="w-4 h-4 text-gray-400" />
              </div>
              {searchKeyword && (
                <div
                  className="absolute right-2.5 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={
                    () => setSearchKeyword('')
                    // If there was a search keyword before, reset the search
                    // if (searchKeyword) {
                    //   setPageIndex(1);
                    //   setTimeout(() => fetchRequests(1, pageSize), 0);
                    // }
                  }
                >
                  <X className="w-4 h-4 text-gray-400" />
                </div>
              )}
            </div>

            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline">Filter</Button>
              </PopoverTrigger>
              <PopoverContent className="w-[380px]">
                <h1 className="pb-3 font-semibold">Filter</h1>

                <div className="flex flex-col justify-start items-end self-stretch flex-grow-0 flex-shrink-0 gap-4">
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                    <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 h-8 relative pl-[13px] pr-px py-px rounded-md border border-[#d0d3d6]">
                      <p className="flex-grow-0 flex-shrink-0 text-[13.5625px] text-left text-[#8f959e]">
                        Status
                      </p>
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0">
                        <Popover
                          open={openStatusFilterBox}
                          onOpenChange={setOpenStatusFilterBox}
                        >
                          <PopoverTrigger asChild>
                            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-8 space-x-[-0.009999999776482582px] px-[11px] py-px rounded-md cursor-pointer">
                              <div className="flex flex-col justify-center items-start self-stretch flex-grow relative overflow-hidden">
                                <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0 h-7 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13.5625px] text-left text-[#1f2329]">
                                    {draftStatus
                                      ? requestStatusOptions.find(
                                          (option) =>
                                            option.value === draftStatus,
                                        )?.label
                                      : 'All'}
                                  </p>
                                </div>
                              </div>
                              <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0 h-7 pl-2">
                                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-7 py-2">
                                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                    <svg
                                      width={12}
                                      height={12}
                                      viewBox="0 0 12 12"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
                                      preserveAspectRatio="none"
                                    >
                                      <path
                                        d="M1.70692 3.54313L1.35342 3.89663C1.25969 3.99039 1.20703 4.11755 1.20703 4.25013C1.20703 4.38271 1.25969 4.50986 1.35342 4.60363L5.24242 8.49263C5.33529 8.58553 5.44555 8.65923 5.5669 8.70951C5.68825 8.75979 5.81832 8.78566 5.94967 8.78566C6.08103 8.78566 6.2111 8.75979 6.33245 8.70951C6.4538 8.65923 6.56406 8.58553 6.65692 8.49263L10.5459 4.60363C10.6397 4.50986 10.6923 4.38271 10.6923 4.25013C10.6923 4.11755 10.6397 3.99039 10.5459 3.89663L10.1924 3.54313C10.146 3.49664 10.0908 3.45976 10.0301 3.4346C9.96945 3.40944 9.90438 3.39648 9.83867 3.39648C9.77297 3.39648 9.7079 3.40944 9.6472 3.4346C9.58651 3.45976 9.53136 3.49664 9.48492 3.54313L5.94992 7.07813L2.41392 3.54313C2.32016 3.44939 2.19301 3.39673 2.06042 3.39673C1.92784 3.39673 1.80069 3.44939 1.70692 3.54313Z"
                                        fill="#646A73"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </PopoverTrigger>
                          <PopoverContent className="w-[200px] p-0 !shadow-none !border-[none]">
                            <Command>
                              <CommandList>
                                <CommandEmpty>No data</CommandEmpty>
                                <CommandGroup>
                                  {requestStatusOptions.map((option) => (
                                    <CommandItem
                                      key={option.value}
                                      value={option.value}
                                      onSelect={(currentValue) => {
                                        // Only update if the value actually changed
                                        const newValue =
                                          currentValue === draftStatus
                                            ? ''
                                            : currentValue;
                                        if (newValue !== draftStatus) {
                                          setDraftStatus(newValue);
                                        }
                                        setOpenStatusFilterBox(false);
                                      }}
                                    >
                                      {option.label}
                                      <Check
                                        className={cn(
                                          'ml-auto',
                                          draftStatus === option.value
                                            ? 'opacity-100'
                                            : 'opacity-0',
                                        )}
                                      />
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    {/* Date Range Filter */}
                    <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 h-8 relative pl-[13px] pr-px py-px rounded-md border border-[#d0d3d6]">
                      <p className="flex-grow-0 flex-shrink-0 text-[13.5625px] text-left text-[#8f959e]">
                        Date Range
                      </p>
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0">
                        <Popover>
                          <PopoverTrigger asChild>
                            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-8 space-x-[-0.009999999776482582px] px-[11px] py-px rounded-md cursor-pointer">
                              <div className="flex flex-col justify-center items-start self-stretch flex-grow relative overflow-hidden">
                                <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0 h-7 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13.5625px] text-left text-[#1f2329]">
                                    {draftDate?.from ? (
                                      draftDate.to ? (
                                        <>
                                          {formatDateString(
                                            draftDate.from,
                                            false,
                                          )}{' '}
                                          -{' '}
                                          {formatDateString(
                                            draftDate.to,
                                            false,
                                          )}
                                        </>
                                      ) : (
                                        formatDateString(draftDate.from, false)
                                      )
                                    ) : (
                                      'All'
                                    )}
                                  </p>
                                </div>
                              </div>
                              <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0 h-7 pl-2">
                                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-7 py-2">
                                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                    <svg
                                      width={12}
                                      height={12}
                                      viewBox="0 0 12 12"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
                                      preserveAspectRatio="none"
                                    >
                                      <path
                                        d="M1.70692 3.54313L1.35342 3.89663C1.25969 3.99039 1.20703 4.11755 1.20703 4.25013C1.20703 4.38271 1.25969 4.50986 1.35342 4.60363L5.24242 8.49263C5.33529 8.58553 5.44555 8.65923 5.5669 8.70951C5.68825 8.75979 5.81832 8.78566 5.94967 8.78566C6.08103 8.78566 6.2111 8.75979 6.33245 8.70951C6.4538 8.65923 6.56406 8.58553 6.65692 8.49263L10.5459 4.60363C10.6397 4.50986 10.6923 4.38271 10.6923 4.25013C10.6923 4.11755 10.6397 3.99039 10.5459 3.89663L10.1924 3.54313C10.146 3.49664 10.0908 3.45976 10.0301 3.4346C9.96945 3.40944 9.90438 3.39648 9.83867 3.39648C9.77297 3.39648 9.7079 3.40944 9.6472 3.4346C9.58651 3.45976 9.53136 3.49664 9.48492 3.54313L5.94992 7.07813L2.41392 3.54313C2.32016 3.44939 2.19301 3.39673 2.06042 3.39673C1.92784 3.39673 1.80069 3.44939 1.70692 3.54313Z"
                                        fill="#646A73"
                                      />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="range"
                              defaultMonth={draftDate?.from}
                              selected={draftDate}
                              onSelect={(
                                newDateRange: DateRange | undefined,
                              ) => {
                                // Only update if the date range actually changed
                                const currentFrom =
                                  draftDate?.from?.toISOString();
                                const currentTo = draftDate?.to?.toISOString();
                                const newFrom =
                                  newDateRange?.from?.toISOString();
                                const newTo = newDateRange?.to?.toISOString();

                                if (
                                  currentFrom !== newFrom ||
                                  currentTo !== newTo
                                ) {
                                  setDraftDate(newDateRange);
                                }
                              }}
                              numberOfMonths={2}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end items-center self-stretch flex-grow-0 flex-shrink-0 gap-3">
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                      <div
                        className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative px-1 py-0.5 rounded-md cursor-pointer"
                        onClick={clearAllFilters}
                      >
                        <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                          Clear filter
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                      <div
                        className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-7 relative px-2 py-1 rounded-md bg-white border border-[#d0d3d6] cursor-pointer"
                        onClick={() => setOpen(false)}
                      >
                        <p className="flex-grow-0 flex-shrink-0 text-[11.8125px] font-medium text-center text-[#1f2329]">
                          Cancel
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                      <div
                        className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-7 relative px-[9.15999984741211px] py-1 rounded-md bg-[#008fd3] border border-[#008fd3] cursor-pointer"
                        onClick={handleApply}
                      >
                        <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-white">
                          Filter
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div className="flex flex-col justify-start items-start self-stretch flex-grow flex-shrink-0 gap-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <p>Loading data...</p>
              </div>
            ) : (
              <DataTable
                columns={columns}
                data={requestData}
                pageCount={totalPages}
                pageSize={pageSize}
                pageIndex={pageIndex - 1}
                onPaginationChange={handlePaginationChange}
                sizeChanger={true}
                manualPagination={true}
                isLoading={isLoading}
                columnWidths={{
                  code: 50,
                  supervisorName: 200,
                  supervisorDepartment: 150,
                  timeIn: 180,
                  timeOut: 180,
                  status: 120,
                  actions: 100,
                }}
              />
            )}
          </div>
        </div>
      </div>
    </ProtectedLayout>
  );
}
