import * as React from 'react';
import { cn } from '@/lib/utils';
import HeaderLayout from './headerLayout';
import SiderLayout from './siderLayout';
import { SidebarProvider } from '@/components/ui/sidebar';
import { useNavigate } from '@tanstack/react-router';
import { useAuth } from '@/hooks/use-auth';

interface ProtectedLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function ProtectedLayout({
  children,
  className,
  ...props
}: ProtectedLayoutProps) {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Redirect to sign-in if not authenticated
  React.useEffect(() => {
    if (!isAuthenticated()) {
      navigate({ to: '/auth/sign-in' });
    }
  }, [isAuthenticated, navigate]);

  if (!isAuthenticated()) {
    return null;
  }

  return (
    <div
      className={cn('h-screen flex flex-col overflow-hidden', className)}
      {...props}
    >
      {/* Header section - full width, positioned at the top */}
      <div className="w-full z-50 flex-shrink-0">
        <HeaderLayout />
      </div>

      {/* Body section with fixed height */}
      <div className="flex flex-1 overflow-hidden">
        <SidebarProvider>
          {/* Sidebar and content container */}
          <div className="flex w-full min-h-0">
            {/* Sidebar with fixed height */}
            <div className="h-full flex-shrink-0">
              <SiderLayout />
            </div>

            {/* Main content - only this will scroll */}
            <main className="flex-1 overflow-auto transition-all duration-200 pb-[65px]">
              <div className="p-6">{children}</div>
            </main>
          </div>
        </SidebarProvider>
      </div>
    </div>
  );
}

export default ProtectedLayout;
