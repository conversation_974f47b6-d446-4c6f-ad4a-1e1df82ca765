import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { RequestAttributes, RequestStatus } from '@c-visitor/types';

// Request interface for Mongoose document
export interface RequestDocument extends Omit<RequestAttributes, 'id'>, Document {}

// Request schema for Mongoose
const RequestSchema = new Schema<RequestDocument>(
  {
    code: { type: Number, unique: true, required: true, autoIncrement: true },
    companyName: { type: String, required: true, trim: true },
    purpose: { type: String, required: true, trim: true },
    contactName: { type: String, required: true, trim: true },
    contactDepartment: { type: String, required: true, trim: true },
    supervisorName: { type: String, required: true, trim: true },
    supervisorDepartment: { type: String, required: true, trim: true },
    supervisorEmail: { type: String, required: true, trim: true },
    supervisorPhone: { type: String, required: true, trim: true },
    timeIn: { type: String, required: true },
    timeOut: { type: String },
    status: {
      type: String,
      enum: Object.values(RequestStatus),
      default: RequestStatus.PENDING,
      required: true,
    },
    deviceInformationBroughtIn: { type: String, trim: true },
    note: { type: String, trim: true },
    otherRequest: { type: String, trim: true },
    reason: { type: String, trim: true }, // New field for rejection reason
    changeStatusBy: {
      type: Schema.Types.Mixed,
    },
    updatedBy: { type: String }, // New field to track who updated the status
  },
  { timestamps: true, collection: 'requests' },
);

// Create indexes for frequently queried fields
RequestSchema.index({ code: 1 }, { unique: true });
RequestSchema.index({ status: 1 });
RequestSchema.index({ supervisorEmail: 1 });

// Create the Request model
const RequestModel = mongoose.model<RequestDocument>('Request', RequestSchema);

export default RequestModel;
