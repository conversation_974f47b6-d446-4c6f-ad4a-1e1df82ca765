import { memo } from 'react';
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { RequestInfoTab } from './request-info-tab';
import { IRequest, IReference } from '@c-visitor/types';
import { RequestEntryHistoryTab } from './request-entry-history-tab';

interface RequestTabsProps {
  tabActive: string;
  setTabActive: (value: string) => void;
  selectedRequest: IRequest | null;
  references: IReference[];
  isLoadingRecognitions?: boolean;
  totalPages?: number;
}

/**
 * Component that manages the tabs for request details
 */
const RequestTabsComponent = ({
  tabActive,
  setTabActive,
  selectedRequest,
  references,
  isLoadingRecognitions = false,
  totalPages,
}: RequestTabsProps) => {
  return (
    <div className="bg-white w-full rounded-lg">
      <Tabs
        defaultValue="info"
        className="w-full"
        onValueChange={setTabActive}
        value={tabActive}
      >
        <TabsList className="flex justify-start w-[100px] bg-transparent p-0 m-0 border-0 border-none ">
          <TabsTrigger
            value="info"
            className=" cursor-pointer flex justify-start items-center gap-1.5 px-5 py-3 rounded-none border-t-0 border-r-0 border-l-0 data-[state=active]:border-b-[3px] data-[state=active]:border-[#008fd3] data-[state=active]:text-[#008fd3] data-[state=inactive]:text-[#8f959e] data-[state=active]:shadow-none bg-transparent"
          >
            <span className="text-sm font-semibold ">General Information</span>
          </TabsTrigger>
          <TabsTrigger
            value="entryHistory"
            className=" cursor-pointer flex justify-start items-center gap-1.5 px-5 py-3 rounded-none border-t-0 border-r-0 border-l-0 data-[state=active]:border-b-[3px] data-[state=active]:border-[#008fd3] data-[state=active]:text-[#008fd3] data-[state=inactive]:text-[#8f959e] data-[state=active]:shadow-none bg-transparent"
          >
            <span className="text-sm font-semibold">Access History</span>
          </TabsTrigger>
        </TabsList>
        <div className="pt-5">
          {tabActive === 'info' && (
            <RequestInfoTab
              selectedRequest={selectedRequest}
              references={references}
            />
          )}
          {tabActive === 'entryHistory' && (
            <RequestEntryHistoryTab
              references={references}
              isLoadingRecognitions={isLoadingRecognitions}
              totalPages={totalPages}
            />
          )}
        </div>
      </Tabs>
    </div>
  );
};

/**
 * Memoized version of RequestTabs to prevent unnecessary re-renders
 * Only re-renders when tab changes or data changes
 */
export const RequestTabs = memo(
  RequestTabsComponent,
  (prevProps, nextProps) => {
    // Compare important fields that affect rendering
    const referencesChanged =
      prevProps.references?.length !== nextProps.references?.length ||
      JSON.stringify(prevProps.references) !==
        JSON.stringify(nextProps.references);

    return (
      prevProps.tabActive === nextProps.tabActive &&
      prevProps.isLoadingRecognitions === nextProps.isLoadingRecognitions &&
      prevProps.totalPages === nextProps.totalPages &&
      !referencesChanged
    );
  },
);
