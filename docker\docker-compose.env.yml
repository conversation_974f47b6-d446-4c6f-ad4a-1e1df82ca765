services:
  mongodb:
    image: mongo:latest
    container_name: c_access_mongodb
    ports:
      - '27017:27017'
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: PasSw0rd
    volumes:
      - mongo_data:/var/lib/mongodb/data
    restart: always
    networks:
      - c-access-network

  minio:
    image: minio/minio:latest
    container_name: c_access_minio
    ports:
      - '9000:9000' # MinIO API
      - '9001:9001' # MinIO Console
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - c-access-network

  postgresql:
    image: postgres:latest
    container_name: c_access_postgresql
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: CVisitor@PasSw0rd
    ports:
      - "15433:5432"
    volumes:
      - ./volumes/postgres_data:/var/lib/postgresql/data
    restart: always
    networks:
      - c-access-network

  redis:
    image: redis:latest
    container_name: c_access_redis
    ports:
      - "6379:6379"
    volumes:
      - ./volumes/redis_data:/data
    command: redis-server --appendonly yes
    restart: always
    networks:
      - c-access-network

volumes:
  mongo_data:
    driver: local
  minio_data:
    driver: local

networks:
  c-access-network:
    driver: bridge
