import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { AnonymousLayout } from '@/components/layout'

export const Route = createFileRoute('/app/createRequest/createRequestSuccessfully')({
  component: CreateRequestSuccessfullyComponent,
})

function CreateRequestSuccessfullyComponent() {
  const navigate = useNavigate()

  const handleBackToRegistration = () => {
    navigate({ to: '/app/create-request' })
  }

  return (
    <AnonymousLayout>
      <div className='self-center'>
        <div className='flex justify-center items-center w-full gap-2.5 p-12 rounded-lg bg-white'>
          <div className='flex flex-col justify-start items-start flex-grow gap-8'>
            <div className='flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-4'>
              <div className='flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2'>
                <svg
                  width={24}
                  height={24}
                  viewBox='0 0 24 24'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                  className='flex-grow-0 flex-shrink-0 w-6 h-6 relative'
                  preserveAspectRatio='none'
                >
                  <path
                    d='M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z'
                    fill='#40BF24'
                    stroke='#40BF24'
                  />
                  <path d='M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8' stroke='white' />
                </svg>
                <p className='flex-grow-0 flex-shrink-0 text-lg font-semibold text-left text-[#1f2329]'>Successfully</p>
              </div>
              <p className='self-stretch flex-grow-0 flex-shrink-0 w-[984px] text-sm text-center !self-center text-[#1f2329]/70'>
                <span className='self-stretch flex-grow-0 flex-shrink-0 w-[984px] text-sm text-center text-[#1f2329]/70'>
                  The registration request has been submitted and is awaiting approval in the next step.
                </span>
                <br />
                <span className='self-stretch flex-grow-0 flex-shrink-0 w-[984px] text-sm text-center text-[#1f2329]/70'>
                  Approval information will be emailed to members on the registration list.
                </span>
              </p>
              <Button onClick={handleBackToRegistration} className='cursor-pointer'>
                Return to registration
              </Button>
            </div>
          </div>
        </div>
      </div>
    </AnonymousLayout>
  )
}
