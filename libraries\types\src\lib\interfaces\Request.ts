import { DateTracking } from '../shared/DateTracking.js';
import { IReference } from './Reference.js';
import { RequestStatus } from '../enums/RequestStatus.js';

/**
 * Interface cho thông tin yêu cầu
 */
export interface IRequest extends RequestAttributes {}

export interface RequestAttributes extends RequestReference, DateTracking {
  id: string;
  // Mã đăng ký (auto-generated)
  code?: number;
  // Tên công ty
  companyName: string;
  // Mục đích
  purpose: string;
  // Người cần gặp
  contactName: string;
  // Bộ phận người cần gặp
  contactDepartment: string;
  // Người phụ trách
  supervisorName: string;
  // Bộ phận người phụ trách
  supervisorDepartment: string;
  // Email người phụ trách
  supervisorEmail: string;
  // Số điện thoại người phụ trách
  supervisorPhone: string;
  // Thời gian vào
  timeIn: string;
  // Thời gian ra
  timeOut: string;
  // Trạng thái
  status: RequestStatus;
  // Thông tin thiết bị mang vào
  deviceInformationBroughtIn: string;
  // Ghi chú
  note?: string;
  // Yêu cầu khác
  otherRequest: string;
  // Lý do từ chối (chỉ có khi status là REJECTED)
  reason?: string;
  // Người thay đổi trạng thái - có thể là string hoặc object user
  changeStatusBy?:
    | string
    | {
        id: string;
        email: string;
        fullName: string;
        emailVerified: boolean;
        active: boolean;
      };
  // ID của người cập nhật trạng thái cuối cùng 
  updatedBy?: string;
  // Thông tin chi tiết của người cập nhật trạng thái (populated từ updatedBy)
  updatedByUser?: {
    id: string;
    email: string;
    fullName: string;
    emailVerified: boolean;
    active: boolean;
  };
}

export interface RequestReference {
  companyId?: string;
  references?: IReference[];
}
